# Modernizing the Dakoii Provincial Government Theme (Single-Column, No Sidebar)

This guide walks you through updating the theme into a modern, professional, single‑column layout without sidebars. It focuses on clean typography, modern CSS, accessibility, and performance—while keeping the theme lightweight and maintainable.

Note: The theme name has been updated already to “Dakoii Provincial Government Theme Modern” in style.css.

## 1) Before You Start
- Create a full backup (theme folder + database) via your preferred method (e.g., XAMPP copy of htdocs + phpMyAdmin export).
- If the theme is active, plan a maintenance window or work on a staging site.

## 2) Target Outcome
- Single-column layout across all templates (no sidebar.php usage).
- Consistent content width and vertical rhythm.
- Modern color system using CSS custom properties.
- Refined typography (scalable, legible, minimal weights).
- Accessible components (contrast, focus states, skip links).
- Better performance (reduced DOM/CSS bloat, minimal JS).

## 3) Theme Name & Metadata
- Already done: In style.css header, the Theme Name is now:
  - Theme Name: Dakoii Provincial Government Theme Modern
- Keep Text Domain unchanged unless you are also refactoring translations.

## 4) Remove Sidebars (Code-Level Changes)

You will make small, safe template edits to remove sidebar usage. Keep sidebar.php in the repo if you want to revert later, but don’t include it in templates.

Templates to check and update:
- index.php
- single.php
- page.php
- archive.php
- search.php
- 404.php

Steps:
1. Remove get_sidebar() calls from each template listed above.
2. Ensure the main content wrapper spans full available width (we’ll constrain with a max-width container via CSS).
3. In functions.php, if a sidebar (widget area) was registered, optionally unregister it to prevent user confusion.

Example (functions.php):

```php
// Optionally disable the old sidebar/widget area to simplify the UI.
add_action('widgets_init', function () {
    // If you previously registered a sidebar with register_sidebar([...])
    // and want to disable it in the modern theme, you can skip re-registering it
    // or call unregister_sidebar('primary'); // Use your actual sidebar ID
});
```

## 5) Establish a Single-Column Layout (CSS)

Core ideas:
- Use a centered container class to limit line length.
- Provide comfortable spacing (margin/padding) and readable line-height.
- Ensure images and embeds are responsive.

Add or refine in style.css (examples — adapt to your structure):

```css
:root {
  /* Neutral, modern palette – adjust to your brand */
  --bg: #0e0f12;
  --surface: #121418;
  --text: #e6e8ee;
  --muted: #9aa3b2;
  --accent: #4f8cff; /* Accessible blue accent */
  --border: #1e2127;
  --maxw: 72ch; /* Comfortable line length */
}

body {
  background: var(--bg);
  color: var(--text);
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
  line-height: 1.7;
}

.site-wrap {
  width: 100%;
  margin-inline: auto;
  padding: 24px;
}

.container {
  width: 100%;
  max-width: var(--maxw);
  margin-inline: auto;
}

main,
.entry-content {
  display: block;
}

img,
video,
iframe {
  max-width: 100%;
  height: auto;
}

hr { border-color: var(--border); }

a { color: var(--accent); }

/* Spacing scale */
:root { --space-1: 8px; --space-2: 12px; --space-3: 16px; --space-4: 24px; --space-5: 32px; --space-6: 48px; }

h1 { font-size: clamp(1.75rem, 2.5vw, 2.25rem); line-height: 1.2; margin: var(--space-5) 0 var(--space-3); }
h2 { font-size: clamp(1.5rem, 2vw, 1.75rem); line-height: 1.3; margin: var(--space-4) 0 var(--space-2); }
h3 { font-size: clamp(1.25rem, 1.5vw, 1.375rem); line-height: 1.35; margin: var(--space-3) 0 var(--space-2); }
p  { margin: 0 0 var(--space-3); }

/* Remove any legacy multi-column/layout grids if present */
.content-area,
.site-content { display: block; width: 100%; }

/* Optional subtle card surface */
.section,
.article-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: var(--space-4);
}
```

Use a single, centered container in header.php and footer.php as well to align everything on the same vertical column.

## 6) Template Structure (HTML)

Keep markup minimal and semantic. Example structure in index.php/single.php:

```php
<?php get_header(); ?>
<div class="site-wrap">
  <main id="primary" class="site-main">
    <div class="container">
      <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class('article-card'); ?>>
          <header>
            <h1 class="entry-title"><?php the_title(); ?></h1>
          </header>
          <div class="entry-content">
            <?php the_content(); ?>
          </div>
        </article>
      <?php endwhile; endif; ?>
    </div>
  </main>
</div>
<?php get_footer(); ?>
```

Key points:
- No call to get_sidebar().
- main wraps a .container with max-width.
- article uses a subtle surface to improve readability on dark backgrounds.

## 7) Navigation and Header
- Keep the header compact with clear hierarchy.
- Use the same .container structure for consistency.
- Ensure focus-visible styles for keyboard users:

```css
:focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}
```

## 8) Gutenberg/Block Editor Considerations
- Consider adding theme support for wide/full alignment in functions.php if you later add multi-section landing pages.

```php
add_action('after_setup_theme', function () {
    add_theme_support('align-wide');
});
```

- For a strict single-column theme, you may choose not to use full-wide blocks, but align-wide can still help with hero/cover blocks when needed.

## 9) Performance & Clean-Up
- Remove unused CSS and legacy layout rules tied to sidebars.
- Avoid loading large font files; prefer system fonts or a single variable font.
- Defer or remove non-essential JS, keep interactions minimal.
- Optimize images; use modern formats when possible (WebP/AVIF) and correct sizes.

## 10) Accessibility
- Check color contrast (WCAG AA at minimum). Dark backgrounds require sufficient text contrast.
- Ensure headings follow a logical order (one H1 per page).
- Provide skip-to-content link after the body tag for keyboard users.

Example:
```html
<a class="skip-link" href="#primary">Skip to content</a>
```

## 11) Testing Checklist (XAMPP)
- View the frontend pages (home, single post, page, archives, search, 404) to confirm single-column layout everywhere.
- Confirm there are no PHP notices (enable WP_DEBUG on staging if needed).
- Verify links, focus states, and keyboard navigation.
- Test responsive behavior from 320px up to desktop.

## 12) Optional: Dark/Light Toggle (Later)
- If you want a light mode later, you can define a light color set and toggle with a class on the html/body element. Keep out of scope for now to stay minimal.

## 13) Maintenance
- Document any custom CSS/components you add in this dev_guide folder.
- Keep the palette and spacing tokens centralized in :root so changes cascade consistently.

---

If you need me to apply these changes to the templates and CSS now (removing sidebars and enforcing the single‑column container), say “Apply the single‑column update” and I’ll proceed safely across the relevant files.

