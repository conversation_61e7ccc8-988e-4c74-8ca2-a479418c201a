<?php
/**
 * The template for displaying all single posts
 *
 * @package Nols_ESPA_Theme_Two
 */

get_header(); ?>

<div class="site-wrap">
  <main id="primary" class="site-main">
    <div class="container">
      <?php while (have_posts()) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class('article-card'); ?>>
          <header>
            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
            <div class="entry-meta">
              <?php
              printf(
                esc_html__('Published on %1$s | By %2$s', 'dakoii-provincial-government-theme'),
                '<time datetime="' . esc_attr(get_the_date('c')) . '">' . esc_html(get_the_date()) . '</time>',
                '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
              );
              ?>
              <?php if (has_category()) : ?>
                <span class="categories"> | <?php esc_html_e('Categories:', 'dakoii-provincial-government-theme'); ?> <?php the_category(', '); ?></span>
              <?php endif; ?>
              <?php if (has_tag()) : ?>
                <span class="tags"> | <?php esc_html_e('Tags:', 'dakoii-provincial-government-theme'); ?> <?php the_tags('', ', '); ?></span>
              <?php endif; ?>
              <?php if (comments_open() || get_comments_number()) : ?>
                <span class="comments-link"> | <a href="#comments"><?php comments_number(esc_html__('Leave a comment', 'dakoii-provincial-government-theme'), esc_html__('1 Comment', 'dakoii-provincial-government-theme'), esc_html__('% Comments', 'dakoii-provincial-government-theme')); ?></a></span>
              <?php endif; ?>
            </div>
            <?php if (has_post_thumbnail()) : ?>
              <div class="post-thumbnail">
                <?php the_post_thumbnail('large', array('class' => 'featured-image')); ?>
                <?php if (get_the_post_thumbnail_caption()) : ?>
                  <div class="post-thumbnail-caption"><?php echo wp_kses_post(get_the_post_thumbnail_caption()); ?></div>
                <?php endif; ?>
              </div>
            <?php endif; ?>
          </header>

          <div class="entry-content">
            <?php
            the_content();
            wp_link_pages(array(
              'before' => '<div class="page-links">' . esc_html__('Pages:', 'nols-espa-theme-two'),
              'after'  => '</div>',
            ));
            ?>
          </div>

          <footer class="entry-footer">
            <?php
            $prev_post = get_previous_post();
            $next_post = get_next_post();
            if ($prev_post || $next_post) : ?>
              <nav class="post-navigation" aria-label="<?php esc_attr_e('Post navigation', 'nols-espa-theme-two'); ?>">
                <div class="nav-links">
                  <?php if ($prev_post) : ?>
                    <div class="nav-previous">
                      <a href="<?php echo esc_url(get_permalink($prev_post)); ?>" rel="prev">
                        <span class="nav-subtitle"><?php esc_html_e('Previous Post:', 'nols-espa-theme-two'); ?></span>
                        <span class="nav-title"><?php echo esc_html(get_the_title($prev_post)); ?></span>
                      </a>
                    </div>
                  <?php endif; ?>
                  <?php if ($next_post) : ?>
                    <div class="nav-next">
                      <a href="<?php echo esc_url(get_permalink($next_post)); ?>" rel="next">
                        <span class="nav-subtitle"><?php esc_html_e('Next Post:', 'nols-espa-theme-two'); ?></span>
                        <span class="nav-title"><?php echo esc_html(get_the_title($next_post)); ?></span>
                      </a>
                    </div>
                  <?php endif; ?>
                </div>
              </nav>
            <?php endif; ?>

            <?php if (get_the_author_meta('description')) : ?>
              <div class="author-info">
                <div class="author-avatar"><?php echo get_avatar(get_the_author_meta('user_email'), 80); ?></div>
                <div class="author-description">
                  <h3 class="author-title">
                    <?php printf(esc_html__('About %s', 'nols-espa-theme-two'), '<span class="author-name">' . esc_html(get_the_author()) . '</span>'); ?>
                  </h3>
                  <div class="author-bio"><?php echo wp_kses_post(get_the_author_meta('description')); ?></div>
                  <div class="author-link">
                    <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>"><?php esc_html_e('View all posts by', 'nols-espa-theme-two'); ?> <?php echo esc_html(get_the_author()); ?></a>
                  </div>
                </div>
              </div>
            <?php endif; ?>
          </footer>
        </article>

        <?php
        $related_posts = get_posts(array(
          'category__in'   => wp_get_post_categories(get_the_ID()),
          'numberposts'    => 3,
          'post__not_in'   => array(get_the_ID()),
          'orderby'        => 'rand',
        ));
        if ($related_posts) : ?>
          <section class="related-posts">
            <h3 class="related-posts-title"><?php esc_html_e('Related Posts', 'dakoii-provincial-government-theme'); ?></h3>
            <div class="related-posts-grid">
              <?php foreach ($related_posts as $post) : setup_postdata($post); ?>
                <article class="related-post">
                  <?php if (has_post_thumbnail()) : ?>
                    <div class="related-post-thumbnail">
                      <a href="<?php the_permalink(); ?>"><?php the_post_thumbnail('medium'); ?></a>
                    </div>
                  <?php endif; ?>
                  <div class="related-post-content">
                    <h4 class="related-post-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                    <div class="related-post-meta"><?php echo esc_html(get_the_date()); ?></div>
                    <div class="related-post-excerpt"><?php echo wp_trim_words(get_the_excerpt(), 15); ?></div>
                  </div>
                </article>
              <?php endforeach; wp_reset_postdata(); ?>
            </div>
          </section>
        <?php endif; ?>

        <?php if (comments_open() || get_comments_number()) : comments_template(); endif; ?>
      <?php endwhile; ?>
    </div>
  </main>
</div>

<?php get_footer(); ?>
