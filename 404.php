<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package Dakoii_Provincial_Government_Theme
 */

get_header(); ?>

<div class="site-wrap">
  <main id="primary" class="site-main">
    <div class="container">
      <section class="error-404 not-found article-card">
        <header class="page-header">
          <h1 class="page-title"><?php esc_html_e('Oops! That page can&rsquo;t be found.', 'dakoii-provincial-government-theme'); ?></h1>
        </header>

        <div class="page-content">
          <div class="error-message">
            <div class="error-icon">🐦</div>
            <p><?php esc_html_e('It looks like nothing was found at this location. The page you are looking for might have been moved, deleted, or perhaps you just mistyped the URL.', 'dakoii-provincial-government-theme'); ?></p>
          </div>

          <div class="error-search">
            <h3><?php esc_html_e('Try searching for what you need:', 'dakoii-provincial-government-theme'); ?></h3>
            <?php get_search_form(); ?>
          </div>

          <div class="error-suggestions">
            <h3><?php esc_html_e('Here are some helpful links instead:', 'dakoii-provincial-government-theme'); ?></h3>
            <div class="suggestions-grid">
              <div class="suggestion-box">
                <h4><?php esc_html_e('Recent Posts', 'dakoii-provincial-government-theme'); ?></h4>
                <ul>
                  <?php
                  $recent_posts = wp_get_recent_posts(array('numberposts' => 5, 'post_status' => 'publish'));
                  if ($recent_posts) :
                    foreach ($recent_posts as $post) : ?>
                      <li><a href="<?php echo esc_url(get_permalink($post['ID'])); ?>"><?php echo esc_html($post['post_title']); ?></a></li>
                    <?php endforeach; else : ?>
                      <li><?php esc_html_e('No posts found.', 'dakoii-provincial-government-theme'); ?></li>
                  <?php endif; ?>
                </ul>
              </div>
              <div class="suggestion-box">
                <h4><?php esc_html_e('Categories', 'dakoii-provincial-government-theme'); ?></h4>
                <ul>
                  <?php
                  $categories = get_categories(array('orderby' => 'name', 'order' => 'ASC', 'number' => 5));
                  if ($categories) :
                    foreach ($categories as $category) : ?>
                      <li><a href="<?php echo esc_url(get_category_link($category->term_id)); ?>"><?php echo esc_html($category->name); ?> (<?php echo $category->count; ?>)</a></li>
                    <?php endforeach; else : ?>
                      <li><a href="#"><?php esc_html_e('Traditional Arts', 'dakoii-provincial-government-theme'); ?></a></li>
                      <li><a href="#"><?php esc_html_e('Cultural Events', 'dakoii-provincial-government-theme'); ?></a></li>
                      <li><a href="#"><?php esc_html_e('Local History', 'dakoii-provincial-government-theme'); ?></a></li>
                      <li><a href="#"><?php esc_html_e('Community', 'dakoii-provincial-government-theme'); ?></a></li>
                  <?php endif; ?>
                </ul>
              </div>
              <div class="suggestion-box">
                <h4><?php esc_html_e('Quick Links', 'dakoii-provincial-government-theme'); ?></h4>
                <ul>
                  <li><a href="<?php echo esc_url(home_url('/')); ?>"><?php esc_html_e('Home Page', 'dakoii-provincial-government-theme'); ?></a></li>
                  <?php
                  $pages = get_pages(array('sort_order' => 'ASC', 'sort_column' => 'menu_order', 'number' => 4));
                  foreach ($pages as $page) : ?>
                    <li><a href="<?php echo esc_url(get_permalink($page->ID)); ?>"><?php echo esc_html($page->post_title); ?></a></li>
                  <?php endforeach; ?>
                  <?php if (get_option('show_on_front') == 'posts') : ?>
                    <li><a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>"><?php esc_html_e('Blog', 'dakoii-provincial-government-theme'); ?></a></li>
                  <?php endif; ?>
                </ul>
              </div>
            </div>
          </div>

          <div class="back-home">
            <a href="<?php echo esc_url(home_url('/')); ?>" class="read-more"><?php esc_html_e('← Back to Home', 'dakoii-provincial-government-theme'); ?></a>
          </div>
        </div>
      </section>
    </div>
  </main>
</div>

<?php get_footer(); ?>
