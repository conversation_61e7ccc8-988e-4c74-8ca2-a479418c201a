<?php
/**
 * The template for displaying all pages
 *
 * @package Dakoii_Provincial_Government_Theme
 */

get_header(); ?>

<div class="site-wrap">
  <main id="primary" class="site-main">
    <div class="container">
      <?php while (have_posts()) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class('article-card'); ?>>
          <header>
            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
            <?php if (has_post_thumbnail()) : ?>
              <div class="post-thumbnail">
                <?php the_post_thumbnail('large', array('class' => 'featured-image')); ?>
                <?php if (get_the_post_thumbnail_caption()) : ?>
                  <div class="post-thumbnail-caption"><?php echo wp_kses_post(get_the_post_thumbnail_caption()); ?></div>
                <?php endif; ?>
              </div>
            <?php endif; ?>
          </header>

          <div class="entry-content">
            <?php
            the_content();
            wp_link_pages(array(
              'before' => '<div class="page-links">' . esc_html__('Pages:', 'dakoii-provincial-government-theme'),
              'after'  => '</div>',
            ));
            ?>
          </div>

          <?php if (get_edit_post_link()) : ?>
            <footer class="entry-footer">
              <div class="edit-link">
                <?php
                edit_post_link(
                  sprintf(
                    wp_kses(
                      __('Edit <span class="screen-reader-text">%s</span>', 'dakoii-provincial-government-theme'),
                      array('span' => array('class' => array()))
                    ),
                    get_the_title()
                  ),
                  '<span class="edit-link">',
                  '</span>'
                );
                ?>
              </div>
            </footer>
          <?php endif; ?>
        </article>

        <?php if (comments_open() || get_comments_number()) : comments_template(); endif; ?>
      <?php endwhile; ?>
    </div>
  </main>
</div>

<?php get_footer(); ?>
