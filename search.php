<?php
/**
 * The template for displaying search results pages
 *
 * @package Nols_ESPA_Theme_Two
 */

get_header(); ?>

<div class="site-wrap">
  <main id="primary" class="site-main">
    <div class="container">
      <header class="page-header">
        <h1 class="page-title">
          <?php printf(esc_html__('Search Results for: %s', 'nols-espa-theme-two'), '<span class="search-term">' . get_search_query() . '</span>'); ?>
        </h1>
        <div class="search-form-container">
          <p><?php esc_html_e('Refine your search:', 'nols-espa-theme-two'); ?></p>
          <?php get_search_form(); ?>
        </div>
      </header>

      <?php if (have_posts()) : ?>
        <div class="search-results-info">
          <?php
          global $wp_query;
          $total_results = $wp_query->found_posts;
          printf(esc_html(_n('Found %d result', 'Found %d results', $total_results, 'dakoii-provincial-government-theme')), $total_results);
          ?>
        </div>

        <?php while (have_posts()) : the_post(); ?>
          <article id="post-<?php the_ID(); ?>" <?php post_class('article-card search-result'); ?>>
            <header>
              <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>
              <div class="entry-meta">
                <?php
                printf(
                  esc_html__('Published on %1$s | By %2$s', 'nols-espa-theme-two'),
                  '<time datetime="' . esc_attr(get_the_date('c')) . '">' . esc_html(get_the_date()) . '</time>',
                  '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
                );
                ?>
                <?php if (has_category()) : ?>
                  <span class="categories"> | <?php esc_html_e('Categories:', 'nols-espa-theme-two'); ?> <?php the_category(', '); ?></span>
                <?php endif; ?>
                <span class="post-type"> | <?php esc_html_e('Type:', 'nols-espa-theme-two'); ?> <?php echo esc_html(ucfirst(get_post_type())); ?></span>
              </div>
            </header>

            <?php if (has_post_thumbnail()) : ?>
              <div class="search-thumbnail">
                <a href="<?php the_permalink(); ?>"><?php the_post_thumbnail('medium'); ?></a>
              </div>
            <?php endif; ?>

            <div class="entry-content">
              <?php
              $excerpt = get_the_excerpt();
              $search_query = get_search_query();
              if ($search_query && $excerpt) {
                $highlighted_excerpt = preg_replace('/(' . preg_quote($search_query, '/') . ')/i', '<mark>$1</mark>', $excerpt);
                echo wp_kses_post($highlighted_excerpt);
              } else {
                the_excerpt();
              }
              ?>
              <a href="<?php echo esc_url(get_permalink()); ?>" class="read-more"><?php esc_html_e('Read More →', 'dakoii-provincial-government-theme'); ?></a>
            </div>
          </article>
        <?php endwhile; ?>

        <?php the_posts_pagination(array(
          'mid_size' => 2,
          'prev_text' => __('← Previous', 'dakoii-provincial-government-theme'),
          'next_text' => __('Next →', 'dakoii-provincial-government-theme'),
          'class' => 'pagination',
        )); ?>

      <?php else : ?>
        <section class="no-results not-found">
          <header class="page-header">
            <h2 class="page-title"><?php esc_html_e('Nothing found', 'dakoii-provincial-government-theme'); ?></h2>
          </header>
          <div class="page-content">
            <div class="no-results-message">
              <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'nols-espa-theme-two'); ?></p>
            </div>
            <div class="search-suggestions">
              <h3><?php esc_html_e('Search Suggestions:', 'nols-espa-theme-two'); ?></h3>
              <ul>
                <li><?php esc_html_e('Make sure all words are spelled correctly.', 'nols-espa-theme-two'); ?></li>
                <li><?php esc_html_e('Try different keywords.', 'nols-espa-theme-two'); ?></li>
                <li><?php esc_html_e('Try more general keywords.', 'nols-espa-theme-two'); ?></li>
                <li><?php esc_html_e('Try fewer keywords.', 'nols-espa-theme-two'); ?></li>
              </ul>
            </div>
            <div class="alternative-content">
              <h3><?php esc_html_e('You might be interested in:', 'nols-espa-theme-two'); ?></h3>
              <div class="suggestions-grid">
                <div class="suggestion-box">
                  <h4><?php esc_html_e('Recent Posts', 'nols-espa-theme-two'); ?></h4>
                  <ul>
                    <?php
                    $recent_posts = wp_get_recent_posts(array(
                      'numberposts' => 3,
                      'post_status' => 'publish'
                    ));
                    foreach ($recent_posts as $post) : ?>
                      <li><a href="<?php echo esc_url(get_permalink($post['ID'])); ?>"><?php echo esc_html($post['post_title']); ?></a></li>
                    <?php endforeach; ?>
                  </ul>
                </div>
                <div class="suggestion-box">
                  <h4><?php esc_html_e('Popular Categories', 'nols-espa-theme-two'); ?></h4>
                  <ul>
                    <?php
                    $categories = get_categories(array(
                      'orderby' => 'count',
                      'order'   => 'DESC',
                      'number'  => 3,
                    ));
                    foreach ($categories as $category) : ?>
                      <li><a href="<?php echo esc_url(get_category_link($category->term_id)); ?>"><?php echo esc_html($category->name); ?> (<?php echo $category->count; ?>)</a></li>
                    <?php endforeach; ?>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
      <?php endif; ?>
    </div>
  </main>
</div>

<?php get_footer(); ?>
