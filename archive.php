<?php
/**
 * The template for displaying archive pages
 *
 * @package Nols_ESPA_Theme_Two
 */

get_header(); ?>

<div class="site-wrap">
  <main id="primary" class="site-main">
    <div class="container">
      <header class="page-header">
        <?php
        the_archive_title('<h1 class="archive-title">', '</h1>');
        the_archive_description('<div class="archive-description">', '</div>');
        ?>
      </header>

      <?php if (have_posts()) : ?>
        <?php while (have_posts()) : the_post(); ?>
          <article id="post-<?php the_ID(); ?>" <?php post_class('article-card'); ?>>
            <header>
              <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>
              <div class="entry-meta">
                <?php
                printf(
                  esc_html__('Published on %1$s | By %2$s', 'nols-espa-theme-two'),
                  '<time datetime="' . esc_attr(get_the_date('c')) . '">' . esc_html(get_the_date()) . '</time>',
                  '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
                );
                ?>
                <?php if (has_category()) : ?>
                  <span class="categories"> | <?php esc_html_e('Categories:', 'nols-espa-theme-two'); ?> <?php the_category(', '); ?></span>
                <?php endif; ?>
                <?php if (has_tag()) : ?>
                  <span class="tags"> | <?php esc_html_e('Tags:', 'nols-espa-theme-two'); ?> <?php the_tags('', ', '); ?></span>
                <?php endif; ?>
              </div>
            </header>

            <?php if (has_post_thumbnail()) : ?>
              <div class="post-thumbnail">
                <a href="<?php the_permalink(); ?>"><?php the_post_thumbnail('medium'); ?></a>
              </div>
            <?php endif; ?>

            <div class="entry-content">
              <?php the_excerpt(); ?>
              <a href="<?php echo esc_url(get_permalink()); ?>" class="read-more"><?php esc_html_e('Read More →', 'dakoii-provincial-government-theme'); ?></a>
            </div>
          </article>
        <?php endwhile; ?>

        <?php the_posts_pagination(array(
          'mid_size' => 2,
          'prev_text' => __('← Previous', 'dakoii-provincial-government-theme'),
          'next_text' => __('Next →', 'dakoii-provincial-government-theme'),
          'class' => 'pagination',
        )); ?>

      <?php else : ?>
        <section class="no-results not-found">
          <header class="page-header">
            <h1 class="page-title"><?php esc_html_e('Nothing here', 'dakoii-provincial-government-theme'); ?></h1>
          </header>
          <div class="page-content">
            <p><?php esc_html_e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'dakoii-provincial-government-theme'); ?></p>
            <?php get_search_form(); ?>
          </div>
        </section>
      <?php endif; ?>
    </div>
  </main>
</div>

<?php get_footer(); ?>
